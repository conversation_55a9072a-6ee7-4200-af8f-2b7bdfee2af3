import { Text } from '@/components/ui/Text/Text'
import { Trans, useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { DescriptionBox } from './DescriptionBox'
import { HeaderDetail } from './HeaderDetail'
import { SuggestionTemplate } from './SuggestionTemplate'

export const MedicalFacultiesDetailContent = () => {
  const { t } = useTranslation()
  return (
    <View className="h-full">
      <HeaderDetail />

      <Text size="body7" className="my-4">
        <Trans
          i18nKey="MES-998"
          components={{
            voice: (
              <Text size="body10" variant="primary">
                {t('MES-999')}
              </Text>
            ),
          }}
        />{' '}
        :
      </Text>

      <DescriptionBox />

      <SuggestionTemplate />
    </View>
  )
}
