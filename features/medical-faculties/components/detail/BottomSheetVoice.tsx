import React, { useEffect, useState } from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withTiming,
} from 'react-native-reanimated'

import MicrophoneIcon from '@/assets/icons/microphone-red.svg'
import { Text } from '@/components/ui/Text/Text'
import { RecordingPresets, useAudioRecorder, useAudioRecorderState } from 'expo-audio'
import { useTranslation } from 'react-i18next'

import PauseIcon from '@/assets/icons/pause-btn.svg'
import PlayIcon from '@/assets/icons/play-btn.svg'

const NUM_BARS = 20

// Component for individual microwave bar
const MicrowaveBar = ({ isPlaying }: { isPlaying: boolean; index: number }) => {
  const height = useSharedValue(2)

  const animatedStyle = useAnimatedStyle(() => {
    return {
      height: height.value,
    }
  })

  useEffect(() => {
    if (isPlaying) {
      const randomHeight = Math.random() * 20 + 2
      const randomDelay = Math.random() * 200
      const randomDuration = Math.random() * 250 + 150

      height.value = withDelay(
        randomDelay,
        withRepeat(
          withTiming(randomHeight, {
            duration: randomDuration,
            easing: Easing.inOut(Easing.ease),
          }),
          -1,
          true,
        ),
      )
    } else {
      height.value = withTiming(2, { duration: 600 })
    }
  }, [isPlaying, height])

  return <Animated.View style={[animatedStyle]} className="w-[2px] bg-custom-danger-600" />
}

export const BottomSheetVoice = () => {
  const { t } = useTranslation()

  const [recordingTime, setRecordingTime] = useState(0)

  const recording = true

  const pulseScale = useSharedValue(1)

  const pulseOpacity = useSharedValue(1)

  const animatedRingStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: pulseScale.value }],
      opacity: pulseOpacity.value,
    }
  })

  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const recorderState = useAudioRecorderState(audioRecorder);

  const record = async () => {
    await audioRecorder.prepareToRecordAsync();
    audioRecorder.record();
  };

  const stopRecording = async () => {
    // The recording will be available on `audioRecorder.uri`.
    await audioRecorder.stop();
  };

  useEffect(() => {
    let timer: number | undefined

    if (recording) {
      pulseScale.value = withRepeat(
        withTiming(1.2, { duration: 1000, easing: Easing.out(Easing.ease) }),
        -1,
        true,
      )
      pulseOpacity.value = withRepeat(
        withTiming(0.4, { duration: 1000, easing: Easing.inOut(Easing.ease) }),
        -1,
        true,
      )

      timer = setInterval(() => {
        setRecordingTime((prevTime) => prevTime + 1)
      }, 1000)
    } else {
      pulseScale.value = 1
      pulseOpacity.value = 1
      return () => {
        if (timer) {
          clearInterval(timer)
        }
      }
    }
  }, [pulseScale, pulseOpacity])

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
  }

  return (
    <View style={styles.container}>
      <View style={{ alignItems: 'center', justifyContent: 'center', height: 200, width: 200 }}>
        <Animated.View style={[styles.outerPulseRing, animatedRingStyle]} />

        <MicrophoneIcon style={{ height: 100, width: 100 }} />
      </View>
      <Text size="body3">{formatTime(recordingTime)}</Text>

      <View className="min-h-7 w-[60%] flex-row items-center justify-between gap-[6] px-3">
        {Array.from({ length: NUM_BARS }).map((_, index) => (
          <MicrowaveBar key={index} isPlaying={recording} index={index} />
        ))}
      </View>

      {
        recorderState.isRecording ? <TouchableOpacity onPress={() => stopRecording()}>
          <PauseIcon />
        </TouchableOpacity> : <TouchableOpacity onPress={() => record()}>
          <PlayIcon />
        </TouchableOpacity>
      }

      <TouchableOpacity
        onPress={() => stop()}
        className="mt-4 rounded-[99px] bg-primary-50 px-5 py-3"
      >
        <Text size="button3" variant="primary">
          {t('MES-169')}
        </Text>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    gap: 12,
  },
  outerPulseRing: {
    position: 'absolute',
    width: 160,
    height: 160,
    borderRadius: '100%',
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
  },
})
