import { useRouter } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { Keyboard, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native'

import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import { Text } from '@/components/ui/Text/Text'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { useEffect } from 'react'
import { MedicalFacultiesDetailContent } from '../../components/detail/MedicalFacultiesDetailContent'
import { MedicalFacultiesContent } from '../../layouts/MedicalFacultiesContent'

export const MedicalFacultyDetailWrapper = () => {
  const { t } = useTranslation()

  const router = useRouter()

  const { showLoading, hideLoading } = useLoadingScreen()

  useEffect(() => {
    // hideLoading()
  }, [])

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <View className="sticky top-0 z-10 bg-white ">
        <View className="flex flex-row items-center justify-between gap-2 px-4 py-3 ">
          <TouchableOpacity
            onPressIn={() => {
              if (router.canGoBack()) {
                router.back()
              } else {
                router.replace('/')
              }
            }}
          >
            <ArrowLeftIcon width={16} height={16} />
          </TouchableOpacity>

          <View className="items-center">
            <Text size="body3" variant="primary">
              Khoa nội tổng quát
            </Text>

            <Text size="body9" variant="subdued">
              (総合内科)
            </Text>
          </View>

          <View className="h-[16px] w-[16px]"></View>
        </View>

        <MedicalFacultiesContent>
          <MedicalFacultiesDetailContent />
        </MedicalFacultiesContent>
      </View>
    </TouchableWithoutFeedback>
  )
}
